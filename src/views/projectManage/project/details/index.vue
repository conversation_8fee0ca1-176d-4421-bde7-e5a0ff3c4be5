<template>
    <div class="app-container">
      <el-collapse :value ="activeNames">
        <el-collapse-item title="项目详情" name="1">
          <el-form ref="proForm" :model="form" :rules="proRules" size="small" label-width="120px">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="项目编码" prop="projectCode">
                  <el-input v-model="form.projectCode" style="width: 280px;" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="form.projectName" style="width: 280px;" :disabled="type === 'detail'"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目状态" prop="enabled">
                  <el-select v-model="form.enabled" clearable size="small" placeholder="请选择项目状态" class="filter-item" style="width: 280px" :disabled="type === 'detail'" >
                    <el-option v-for="item in ableStatusExt" :key="item.label" :label="item.value" :value="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="渠道方" prop="flowChannel">
                  <el-select v-model="form.flowChannel" clearable size="small" placeholder="请选择渠道方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                    <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="融担方" prop="guaranteeCode">
                  <el-select v-model="form.guaranteeCode" clearable size="small" placeholder="请选择融担方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                    <el-option v-for="item in guaranteeList" :key="item.guaranteeCode" :label="item.guaranteeNameShort" :value="item.guaranteeCode" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="资金方" prop="capitalChannel">
                  <el-select v-model="form.capitalChannel" clearable size="small" placeholder="请选择资金方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                    <el-option v-for="item in fundList" :key="item.bankChannel" :label="item.capitalNameShort" :value="item.bankChannel" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="项目类型" prop="projectTypeCode">
                  <el-select v-model="form.projectTypeCode" clearable size="small" placeholder="请选择项目类型" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                    <el-option v-for="item in projectTypeCode" :key="item.label" :label="item.value" :value="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目开始日期" prop="startDate">
                  <el-date-picker
                    value-format = "yyyy-MM-dd"
                    v-model="form.startDate"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    style="width: 280px;"
                    :disabled="type === 'detail'">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目结束日期" prop="endDate">
                  <el-date-picker
                    value-format = "yyyy-MM-dd"
                    v-model="form.endDate"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    style="width: 280px;"
                    :disabled="type === 'detail'">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <el-tabs v-model="activeName">
        <el-tab-pane label="产品要素" name="first">
          <el-collapse :value="activeNames">
            <el-collapse-item title="基本要素" name="2">
              <el-form ref="baseForm" :model="form" :rules="baseRules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="产品要素编码" prop="id">
                      <el-input v-model="form.id" style="width: 280px;" disabled/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="产品说明" prop="elements.projectName">
                      <el-input v-model="form.elements.projectName" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对客利率" prop="elements.customerInterestRate">
                      <el-select v-model="form.elements.customerInterestRate" clearable size="small" placeholder="请选择对客利率" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in custRate" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="对资利率" prop="elements.fundingInterestRate">
                      <el-select v-model="form.elements.fundingInterestRate" clearable size="small" placeholder="请选择对资利率" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in bankRate" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item label="借款期限" prop="elements.loanTerms">
                      <el-select v-model="form.elements.loanTerms" clearable size="small" placeholder="请选择借款期限" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in loanTerm" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="对资授信黑暗期" prop="elements.fundingCreditDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.fundingCreditDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateFundingCreditDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.fundingCreditDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateFundingCreditDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对资用信黑暗期" prop="elements.fundingLoanDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.fundingLoanDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateFundingLoanDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.fundingLoanDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateFundingLoanDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对资还款黑暗期" prop="elements.fundingRepayDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.fundingRepayDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateFundingRepayDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.fundingRepayDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateFundingRepayDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="对客授信黑暗期" prop="elements.creditDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.creditDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateElementsCreditDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.creditDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateElementsCreditDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对客用信黑暗期" prop="elements.loanDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.loanDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateElementsLoanDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.loanDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateElementsLoanDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对客还款黑暗期" prop="elements.repayDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.repayDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateElementsRepayDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="form.elements.repayDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="type === 'detail'"
                          @change="validateElementsRepayDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="签章渠道" prop="elementsExt.signChannel">
                      <el-select v-model="form.elementsExt.signChannel" clearable size="small" placeholder="请选择签章渠道" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in signChannel" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="短信发送方" prop="elementsExt.overdueSmsSender">
                      <el-select v-model="form.elementsExt.overdueSmsSender" clearable size="small" placeholder="请选择短信发送方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in SMSChannel" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="授信" name="3">
              <el-form ref="creForm" :model="form" :rules="creRules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="风控模型渠道" prop="elementsExt.riskModelChannel">
                      <el-select v-model="form.elementsExt.riskModelChannel" clearable size="small" placeholder="请选择风控模型渠道" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in riskModelChannel" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="日授信限额(万元)" prop="elements.dailyCreditLimit">
                      <el-input v-model="form.elements.dailyCreditLimit" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="年龄范围(岁)" prop="elements.ageRange">
                      <el-select v-model="form.elements.ageRange" clearable size="small" placeholder="请选择年龄范围" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in ageRang" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="征信查询方" prop="elementsExt.creditQueryParty">
                      <el-select v-model="form.elementsExt.creditQueryParty" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in creditQuery" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="征信上报方" prop="elementsExt.creditReportSender">
                      <el-select v-model="form.elementsExt.creditReportSender" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in creditReport" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="放款" name="4">
              <el-form ref="loanForm" :model="form" :rules="loanRules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="放款渠道" prop="elementsExt.loanPaymentChannel">
                      <el-select v-model="form.elementsExt.loanPaymentChannel" clearable size="small" placeholder="请选放款渠道" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in loanPayChannel" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="扣款绑卡渠道" prop="elementsExt.deductionBindCardChannel">
                      <el-select v-model="form.elementsExt.deductionBindCardChannel" clearable size="small" placeholder="请选择扣款绑卡渠道" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in deductChannel" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="日放款限额(万元)" prop="elements.dailyLoanLimit">
                      <el-input v-model="form.elements.dailyLoanLimit" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="单笔限额(元)" prop="elements.drawableAmountStep">
                      <el-input v-model="form.elements.drawableAmountStep" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否允许续借" prop="elementsExt.renew">
                      <el-select v-model="form.elementsExt.renew" clearable size="small" placeholder="请选择是否允许续借" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in supported" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="还款" name="5">
              <el-form ref="repayForm" :model="form" :rules="repayRules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="逾期宽限期(天)" prop="elementsExt.gracePeriodDays">
                      <el-input v-model="form.elementsExt.gracePeriodDays" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="宽限期类型" prop="elementsExt.gracePeriodType">
                      <el-select v-model="form.elementsExt.gracePeriodType" clearable size="small" placeholder="请选择宽限期类型" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in graceType" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="扣款商户号" prop="elementsExt.deductionMerchantCode">
                      <el-input v-model="form.elementsExt.deductionMerchantCode" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="是否支持对客减免" prop="elementsExt.customerReduction">
                      <el-select v-model="form.elementsExt.customerReduction" clearable size="small" placeholder="请选择是否支持对客减免" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in supported" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否支持对资减免" prop="elementsExt.fundingReduction">
                      <el-select v-model="form.elementsExt.fundingReduction" clearable size="small" placeholder="请选择是否支持对资减免" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in supported" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="催收方" prop="elementsExt.collectionParty">
                      <el-select v-model="form.elementsExt.collectionParty" clearable size="small" placeholder="请选择催收方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in creditQuery" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="是否推送催收数据" prop="elementsExt.pushCollectionData">
                      <el-select v-model="form.elementsExt.pushCollectionData" clearable size="small" placeholder="请选择是否推送催收数据" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in supported" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
          </el-collapse>
          <div style="text-align: center; margin-top: 30px;">
            <el-button @click="goBack">返 回</el-button>
            <el-button type="primary" @click="editProject" :disabled="type === 'detail'">保 存</el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane label="对客协议" name="second" >
          <div class="head-container">
            <el-form :model="agreementQuery" ref="queryForm" :inline="true">
              <el-form-item>
                <label class="el-form-item-label">合同模板类型</label>
                <el-select v-model="agreementForm.contractTemplateType" clearable size="small" placeholder="请选择合同模板类型" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail'" >
                  <el-option v-for="item in fileType" :key="item.label" :label="item.value" :value="item.label" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <label class="el-form-item-label">归属方名称</label>
                <el-input v-model="agreementQuery.templateOwnerName" clearable placeholder="请输入项目名称" style="width: 185px;" class="filter-item" />
              </el-form-item>
              <el-form-item>
                <label class="el-form-item-label">状态</label>
                <el-select v-model="agreementQuery.enabled" clearable size="small" placeholder="请选择项目类型" class="filter-item" style="width: 90px" >
                  <el-option v-for="item in ableStatusExt" :key="item.label" :label="item.value" :value="item.label" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button round size="mini" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
                <el-button round size="mini" icon="el-icon-refresh-left" @click="reset">重置</el-button>
              </el-form-item>
            </el-form>
            <!--列表按钮-->
            <el-button
             :disabled="type === 'detail'"
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="openAgreementDialog('add')"
            >
              新增
            </el-button>
            <el-button
             :disabled="type === 'detail'"
              class="filter-item"
              size="mini"
              type="success"
              icon="el-icon-edit"
              @click="openAgreementDialog('edit')"
            >
              修改
            </el-button>
            <el-button
              class="filter-item"
              size="mini"
              type="info"
              icon="el-icon-document"
              @click="openAgreementDialog('detail')"
            >
              查看
            </el-button>
            <el-button
             :disabled="type === 'detail'"
              class="filter-item"
              size="mini"
              type="danger"
              icon="el-icon-document"
              @click="deleteAgreement"
            >
              删除
            </el-button>
            <el-button
             :disabled="type === 'detail'"
              class="filter-item"
              size="mini"
              type="danger"
              icon="el-icon-circle-close"
              @click="stopUseAgreement"
            >
              启用/停用
            </el-button>
            <!--表单组件-->
            <el-dialog :visible.sync="agreementDialog" :close-on-click-modal="false" title="新增对客协议" width="1000px">
              <el-form ref="agreementForm" :model="agreementForm" :rules="agreementRules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="合同模板编码" prop="templateCode">
                      <el-input v-model="agreementForm.templateCode" style="width: 280px;" :disabled="agreementType === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同模板类型" prop="contractTemplateType">
                      <el-select v-model="agreementForm.contractTemplateType" clearable size="small" placeholder="请选择合同模板类型" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail'" >
                        <el-option v-for="item in fileType" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="资金方签署阶段" prop="capitalLoanStage">
                      <el-select v-model="agreementForm.capitalLoanStage" clearable size="small" placeholder="请选择资金方签署阶段" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail' || capitalLock " @change="capitalLockChange">
                        <el-option v-for="item in signingPhase" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="资产方签署阶段" prop="flowLoanStage">
                      <el-select v-model="agreementForm.flowLoanStage" clearable size="small" placeholder="请选择资产方签署阶段" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail' || flowLock " @change="flowLockChange">
                        <el-option v-for="item in LoanStage" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="归属方" prop="templateOwner">
                      <el-select v-model="agreementForm.templateOwner" clearable size="small" placeholder="请选择归属方" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail'" @change="handleChange">
                        <el-option v-for="item in creditQuery" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="归属方名称" prop="templateOwnerName">
                      <el-select v-model="agreementForm.templateOwnerName" clearable size="small" placeholder="请选择归属方名称" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail'">
                        <el-option v-for="item in creditOwer" :key="item.id" :label="item.name" :value="item.name" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="资金方合同名称" prop="capitalContractName">
                      <el-input v-model="agreementForm.capitalContractName" style="width: 280px;" :disabled="agreementType === 'detail' || capitalLock "/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="资产方合同名称" prop="flowContractName">
                      <el-input v-model="agreementForm.flowContractName" style="width: 280px;" :disabled="agreementType === 'detail' || flowLock "/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="是否融担签章" prop="isRdSignature">
                      <el-select v-model="agreementForm.isRdSignature" clearable size="small" placeholder="请选择是否融担签章" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail'" >
                        <el-option v-for="item in supported" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="签章类型" prop="sealType">
                      <el-select v-model="agreementForm.sealType" clearable size="small" placeholder="请选择签章类型" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail'" >
                        <el-option v-for="item in signChannel" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="签章系统唯一编码" prop="templateNo">
                      <el-input v-model="agreementForm.templateNo" style="width: 280px;" :disabled="agreementType === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="是否回传资金方" prop="isReturnToCapital">
                      <el-select v-model="agreementForm.isReturnToCapital" clearable size="small" placeholder="请选择是否回传资金方" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail'" >
                        <el-option v-for="item in supported" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="是否回传流量方" prop="isReturnToFlow">
                      <el-select v-model="agreementForm.isReturnToFlow" clearable size="small" placeholder="请选择是否回传流量方" class="filter-item" style="width: 280px" :disabled="agreementType === 'detail'" >
                        <el-option v-for="item in supported" :key="item.label" :label="item.value" :value="item.label" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="上传协议" prop="filePath">
                      <input ref="file" type="file" @change="uploadFile" accept=".pdf,.PDF" :disabled="agreementType === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="agreementCancel">取 消</el-button>
                <el-button type="primary" @click="agreementSubmit" :disabled="agreementType === 'detail'">确 认</el-button>
              </div>
            </el-dialog>
            <!--表格渲染-->
            <el-table ref="AgreementTable" border v-loading="agreementLoading" :data="agreementData" size="small" style="width: 100%;" @selection-change="handleAgreementChange">
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" label="序号" width="55" />
              <el-table-column prop="templateCode" label="合同模板编码" />
              <el-table-column prop="contractTemplateType" label="合同模板类型"  :formatter = "(row, column, cellValue) => {return fileType.find(item => item.label === cellValue).value}"/>
              <el-table-column prop="capitalLoanStage" label="资金方签署阶段"  :formatter = "(row, column, cellValue) => {if(cellValue) {return signingPhase.find(item => item.label === cellValue).value}}"/>
              <el-table-column prop="flowLoanStage" label="资产方签署阶段"  :formatter = "(row, column, cellValue) => {if(cellValue) {return LoanStage.find(item => item.label === cellValue).value}}"/>
              <el-table-column prop="templateOwner" label="归属方"  :formatter = "(row, column, cellValue) => {return creditQuery.find(item => item.label === cellValue).value}"/>
              <el-table-column prop="templateOwnerName" label="归属方名称" />
              <el-table-column prop="capitalContractName" label="资金方合同名称" />
              <el-table-column prop="flowContractName" label="资产方合同名称" />
              <el-table-column prop="isRdSignature" label="是否融担签章"  :formatter = "(row, column, cellValue) => {return supported.find(item => item.label === cellValue).value}"/>
              <el-table-column prop="sealType" label="签章类型"  :formatter = "(row, column, cellValue) => {return signChannel.find(item => item.label === cellValue).value}"/>
              <el-table-column prop="templateNo" label="签章系统唯一编码" />
              <el-table-column prop="isReturnToCapital" label="是否回传资金方"  :formatter = "(row, column, cellValue) => {return supported.find(item => item.label === cellValue).value}"/>
              <el-table-column prop="isReturnToFlow" label="是否回传流量方"  :formatter = "(row, column, cellValue) => {return supported.find(item => item.label === cellValue).value}"/>
              <el-table-column prop="enabled" label="状态"  :formatter = "(row, column, cellValue) => {return ableStatusExt.find(item => item.label === cellValue).value}"/>
            </el-table>
            <!--分页组件-->
            <Pagination :pageNum="agreementQuery.pageNum" :total="agreementQuery.total" :pageSize="agreementQuery.pageSize"
              @sizeChange="sizeAgreementChange" @currentChange="currentAgreementChange" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="临时配置项" name="third">
          <!--工具栏-->
          <div class="head-container">
            <!--列表按钮-->
            <el-button
             :disabled="type === 'detail'"
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="openConfigDialog('add')"
            >
              新增
            </el-button>
            <el-button
             :disabled="type === 'detail'"
              class="filter-item"
              size="mini"
              type="success"
              icon="el-icon-edit"
              @click="openConfigDialog('edit')"
            >
              修改
            </el-button>
            <el-button
              class="filter-item"
              size="mini"
              type="info"
              icon="el-icon-document"
              @click="openConfigDialog('detail')"
            >
              查看
            </el-button>
            <el-button
             :disabled="type === 'detail'"
              class="filter-item"
              size="mini"
              type="danger"
              icon="el-icon-circle-close"
              @click="stopUseConfig"
            >
              启用/停用
            </el-button>
            <!--表单组件-->
            <el-dialog :visible.sync="configDialog" :close-on-click-modal="false" :title="configType === 'add' ? '新增临时配置' : configType === 'edit' ? '修改临时配置' : '查看临时配置'" width="1000px">
              <el-form ref="configForm" :model="configForm" :rules="configRules" size="small" label-width="120px">
                <el-row :gutter="24" v-if = "configType !== 'add'">
                  <el-col :span="12">
                    <el-form-item label="产品要素编码" prop="id">
                      <el-input v-model="configForm.id" style="width: 280px;" disabled/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="配置有效期起" prop="tempStartTime">
                      <el-date-picker
                        value-format = "yyyy-MM-dd"
                        v-model="configForm.tempStartTime"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        style="width: 280px;" :disabled="configType === 'detail'">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="配置有效期止" prop="tempEndTime">
                      <el-date-picker
                        value-format = "yyyy-MM-dd"
                        v-model="configForm.tempEndTime"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        style="width: 280px;" :disabled="configType === 'detail'">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="对客授信黑暗期" prop="creditDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="configForm.creditDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="configType === 'detail'"
                          @change="validateCreditDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="configForm.creditDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="configType === 'detail'"
                          @change="validateCreditDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="对客用信黑暗期" prop="loanDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="configForm.loanDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="configType === 'detail'"
                          @change="validateLoanDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="configForm.loanDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="configType === 'detail'"
                          @change="validateLoanDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="对客还款黑暗期" prop="repayDarkHours">
                      <div style="display: flex; align-items: center; width: 280px;">
                        <el-time-picker
                          format="HH:mm"
                          v-model="configForm.repayDarkHoursStart"
                          placeholder="开始时间"
                          style="width: 120px;"
                          :disabled="configType === 'detail'"
                          @change="validateRepayDarkHours">
                        </el-time-picker>
                        <span style="margin: 0 8px;">-</span>
                        <el-time-picker
                          format="HH:mm"
                          v-model="configForm.repayDarkHoursEnd"
                          placeholder="结束时间"
                          style="width: 120px;"
                          :disabled="configType === 'detail'"
                          @change="validateRepayDarkHours">
                        </el-time-picker>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="日放款限额(万元)" prop="dailyLoanLimit">
                      <el-input v-model="configForm.dailyLoanLimit" style="width: 280px;" :disabled="configType === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="configCancel">取 消</el-button>
                <el-button type="primary" @click="configSubmit">确 认</el-button>
              </div>
            </el-dialog>
            <!--表格渲染-->
            <el-table ref="configTable" border v-loading="configLoading" :data="configData" size="small" style="width: 100%;" @selection-change="handleConfigChange">
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" label="序号" width="55" />
              <el-table-column prop="id" label="产品要素编码" />
              <el-table-column prop="temRemark" label="备注" />
              <el-table-column prop="tempStartTime" label="有效期起" />
              <el-table-column prop="tempEndTime" label="有效期止" />
              <el-table-column prop="temEnabled" label="状态" :formatter = "(row, column, cellValue) => {return ableStatusExt.find(item => item.label === cellValue).value}"/>
            </el-table>
            <!--分页组件-->
            <Pagination :pageNum="configQuery.pageNum" :total="configQuery.total" :pageSize="configQuery.pageSize"
              @sizeChange="sizeConfigChange" @currentChange="currentConfigChange" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="其他配置" name="fourth">

        </el-tab-pane>
      </el-tabs>
    </div>
</template>

<script>
import { getFundOwer, getGuaOwer, delAgreement, upload, addConfig, getConfigDtl, addAgreement, updateAgreement, getAgreementDtl, updateTemProject, getAssetList, getGuaranteeList, getFundList, getDetail, updateProject, getAgreementList, getConfigList } from '@/api/projectManage/project'
import { get as getDictByName } from "@/api/system/dictDetail"
import { formatTimeRange, parseTimeString } from '@/utils'

export default {
  name: 'ProjectDtl',
  data() {
    return {
      // 查询参数
      agreementQuery: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      configQuery: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      checkedCities: [],
      checkAll: false,
      isIndeterminate: false,
      cities: [],
      // 字典
      ableStatusExt: [],
      creditQuery: [],
      creditOwer: [],
      projectTypeCode: [],
      fileType: [],
      signingPhase: [],
      supported: [],
      signChannel: [],
      custRate: [],
      loanTerm: [],
      bankRate: [],
      SMSChannel: [],
      riskModelChannel: [],
      ageRang: [],
      creditReport: [],
      loanPayChannel: [],
      deductChannel: [],
      graceType: [],
      LoanStage: [],
      // 选取数据
      selectedRows: {},
      // 弹窗开关
      agreementDialog: false,
      configDialog: false,
      // 资产方选择
      assetList: [],
      // 融担方选择
      guaranteeList: [],
      // 资金方选择
      fundList: [],
      // 新增表单数据
      form: {
        elements: {
          fundingCreditDarkHoursStart: null,
          fundingCreditDarkHoursEnd: null,
          fundingLoanDarkHoursStart: null,
          fundingLoanDarkHoursEnd: null,
          fundingRepayDarkHoursStart: null,
          fundingRepayDarkHoursEnd: null,
          creditDarkHoursStart: null,
          creditDarkHoursEnd: null,
          loanDarkHoursStart: null,
          loanDarkHoursEnd: null,
          repayDarkHoursStart: null,
          repayDarkHoursEnd: null
        },
        elementsExt: {},
        loanTerms: []
      },
      agreementForm: {},
      configForm: {
        creditDarkHoursStart: null,
        creditDarkHoursEnd: null,
        loanDarkHoursStart: null,
        loanDarkHoursEnd: null,
        repayDarkHoursStart: null,
        repayDarkHoursEnd: null
      },
      // 中间变量，用于存储格式化后传给后端的字符串
      submitData: {
        creditDarkHoursStr: '',
        loanDarkHoursStr: '',
        repayDarkHoursStr: ''
      },
      // 基本要素的中间变量
      formSubmitData: {
        fundingCreditDarkHoursStr: '',
        fundingLoanDarkHoursStr: '',
        fundingRepayDarkHoursStr: '',
        creditDarkHoursStr: '',
        loanDarkHoursStr: '',
        repayDarkHoursStr: ''
      },
      configSelect: {},
      agreementSelect: {},
      // 主列表数据
      agreementData: [],
      configData: [],
      // 主列表传入id
      id: undefined,
      type: undefined,
      projectCode: undefined,
      // 加载效果
      agreementLoading: false,
      configLoading: false,
      flowLock: false,
      capitalLock: false,
      configType: '',
      agreementType: '',
      // 文件地址
      adder: '',
      // 折叠开关
      activeNames: ['1', '2', '3', '4', '5', '6'],
      // tab选中
      activeName: 'first',
      // 按钮权限
      permission: {
        add: ['admin', 'projectContract:add'],
        edit: ['admin', 'projectContract:edit'],
        del: ['admin', 'projectContract:del']
      },
      // 校验规则
      proRules: {
        projectCode: [
          { required: true, message: '项目编码不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        enabled: [
          { required: true, message: '项目状态不能为空', trigger: 'blur' }
        ],
        flowChannel: [
          { required: true, message: '渠道方不能为空', trigger: 'blur' }
        ],
        guaranteeCode: [
          { required: true, message: '融担方不能为空', trigger: 'blur' }
        ],
        capitalChannel: [
          { required: true, message: '资金方不能为空', trigger: 'blur' }
        ],
        projectTypeCode: [
          { required: true, message: '项目类型不能为空', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '项目开始日期不能为空', trigger: 'blur' }
        ],
        endDate: [
          { required: true, message: '项目结束日期不能为空', trigger: 'blur' }
        ],
      },

      baseRules: {
        id: [
          { required: true, message: '产品要素编码不能为空', trigger: 'blur' }
        ],
        'elements.projectName': [
          { required: true, message: '产品说明不能为空', trigger: 'blur' }
        ],
        'elements.customerInterestRate': [
          { required: true, message: '对客利率不能为空', trigger: 'blur' }
        ],
        'elements.fundingInterestRate': [
          { required: true, message: '对资利率不能为空', trigger: 'blur' }
        ],
        'elements.loanTerms': [
          { required: true, message: '借款期限不能为空', trigger: 'blur' }
        ],
        'elements.fundingCreditDarkHours': [
          {
            validator: (rule, value, callback) => {
              if (!this.form.elements.fundingCreditDarkHoursStart || !this.form.elements.fundingCreditDarkHoursEnd) {
                callback(new Error('对资授信黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        'elements.fundingLoanDarkHours': [
          {
            validator: (rule, value, callback) => {
              if (!this.form.elements.fundingLoanDarkHoursStart || !this.form.elements.fundingLoanDarkHoursEnd) {
                callback(new Error('对资用信黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        'elements.fundingRepayDarkHours': [
          {
            validator: (rule, value, callback) => {
              if (!this.form.elements.fundingRepayDarkHoursStart || !this.form.elements.fundingRepayDarkHoursEnd) {
                callback(new Error('对资还款黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        'elements.creditDarkHours': [
          {
            validator: (rule, value, callback) => {
              if (!this.form.elements.creditDarkHoursStart || !this.form.elements.creditDarkHoursEnd) {
                callback(new Error('对客授信黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        'elements.loanDarkHours': [
          {
            validator: (rule, value, callback) => {
              if (!this.form.elements.loanDarkHoursStart || !this.form.elements.loanDarkHoursEnd) {
                callback(new Error('对客用信黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        'elements.repayDarkHours': [
          {
            validator: (rule, value, callback) => {
              if (!this.form.elements.repayDarkHoursStart || !this.form.elements.repayDarkHoursEnd) {
                callback(new Error('对客还款黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        'elementsExt.signChannel': [
          { required: true, message: '签章渠道不能为空', trigger: 'blur' }
        ],
        'elementsExt.overdueSmsSender': [
          { required: true, message: '短信发送方不能为空', trigger: 'blur' }
        ],
      },

      creRules: {
        'elementsExt.riskModelChannel': [
          { required: true, message: '风控模型渠道不能为空', trigger: 'blur' }
        ],
        'elements.dailyCreditLimit': [
          { required: true, message: '日授信限额不能为空', trigger: 'blur' }
        ],
        'elements.ageRange': [
          { required: true, message: '年龄范围不能为空', trigger: 'blur' }
        ],
        'elementsExt.creditQueryParty': [
          { required: true, message: '征信查询方不能为空', trigger: 'blur' }
        ],
        'elementsExt.creditReportSender': [
          { required: true, message: '征信上报方不能为空', trigger: 'blur' }
        ],
      },

      loanRules: {
        'elementsExt.loanPaymentChannel': [
          { required: true, message: '放款渠道不能为空', trigger: 'blur' }
        ],
        'elementsExt.deductionBindCardChannel': [
          { required: true, message: '扣款绑卡渠道不能为空', trigger: 'blur' }
        ],
        'elements.dailyLoanLimit': [
          { required: true, message: '日放款限额不能为空', trigger: 'blur' }
        ],
        'elements.drawableAmountStep': [
          { required: true, message: '单笔限额不能为空', trigger: 'blur' }
        ],
        'elementsExt.renew': [
          { required: true, message: '是否允许续借不能为空', trigger: 'blur' }
        ]
      },

      repayRules: {
        'elementsExt.gracePeriodDays': [
          { required: true, message: '逾期宽限期不能为空', trigger: 'blur' }
        ],
        'elementsExt.gracePeriodType': [
          { required: true, message: '宽限期类型不能为空', trigger: 'blur' }
        ],
        'elementsExt.deductionMerchantCode': [
          { required: true, message: '扣款商户号不能为空', trigger: 'blur' }
        ],
        'elementsExt.customerReduction': [
          { required: true, message: '是否支持对客减免不能为空', trigger: 'blur' }
        ],
        'elementsExt.fundingReduction': [
          { required: true, message: '是否支持对资减免不能为空', trigger: 'blur' }
        ],
        'elementsExt.collectionParty': [
          { required: true, message: '催收方不能为空', trigger: 'blur' }
        ],
        'elementsExt.pushCollectionData': [
          { required: true, message: '是否推送催收数据不能为空', trigger: 'blur' }
        ]
      },

      agreementRules: {
        templateCode: [
          { required: true, message: '合同模板编码不能为空', trigger: 'blur' }
        ],
        contractTemplateType: [
          { required: true, message: '合同模板类型不能为空', trigger: 'blur' }
        ],
        templateOwner: [
          { required: true, message: '归属方不能为空', trigger: 'blur' }
        ],
        templateOwnerName: [
          { required: true, message: '归属方名称不能为空', trigger: 'blur' }
        ],
        isRdSignature: [
          { required: true, message: '是否融担签章不能为空', trigger: 'blur' }
        ],
        sealType: [
          { required: true, message: '签章类型不能为空', trigger: 'blur' }
        ],
        templateNo: [
          { required: true, message: '签章系统唯一编码不能为空', trigger: 'blur' }
        ],
        isReturnToCapital: [
          { required: true, message: '是否回传资金方不能为空', trigger: 'blur' }
        ],
        isReturnToFlow: [
          { required: true, message: '是否回传流量方不能为空', trigger: 'blur' }
        ],
        filePath: [
          { required: true, message: '协议文件不能为空', trigger: 'blur' }
        ]
      },
      configRules: {
        tempStartTime: [
          { required: true, message: '配置有效期起不能为空', trigger: 'blur' }
        ],
        tempEndTime: [
          { required: true, message: '配置有效期止不能为空', trigger: 'blur' }
        ],
        creditDarkHours: [
          {
            validator: (rule, value, callback) => {
              if (!this.configForm.creditDarkHoursStart || !this.configForm.creditDarkHoursEnd) {
                callback(new Error('对客授信黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        loanDarkHours: [
          {
            validator: (rule, value, callback) => {
              if (!this.configForm.loanDarkHoursStart || !this.configForm.loanDarkHoursEnd) {
                callback(new Error('对客用信黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        repayDarkHours: [
          {
            validator: (rule, value, callback) => {
              if (!this.configForm.repayDarkHoursStart || !this.configForm.repayDarkHoursEnd) {
                callback(new Error('对客还款黑暗期不能为空'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        dailyLoanLimit: [
          { required: true, message: '日放款限额不能为空', trigger: 'blur' }
        ]
      }
    }
  },

  activated() {
    // 路由更新时执行
    // 获取列表传参
    this.id = this.$route.query.id;
    this.type = this.$route.query.type;
    this.projectCode = this.$route.query.projectCode;
    // 获取项目状态字典
    getDictByName("ableStatusExt").then(res => {
      this.ableStatusExt = res.content;
    });
    // 获取项目类型字典
    getDictByName("projectTypeCode").then(res => {
      this.projectTypeCode = res.content;
    });
    // 获取合同模板类型字典
    getDictByName("fileType").then(res => {
      this.fileType = res.content;
    });
    // 获取签署阶段字典
    getDictByName("signingPhase").then(res => {
      this.signingPhase = res.content;
    });
    // 获取是否字典
    getDictByName("supported").then(res => {
      this.supported = res.content;
    });
    // 获取签章渠道字典
    getDictByName("signChannel").then(res => {
      this.signChannel = res.content;
    });
    // 获取归属方字典
    getDictByName("creditQuery").then(res => {
      this.creditQuery = res.content;
    });
    // 获取对客利率字典
    getDictByName("custRate").then(res => {
      this.custRate = res.content;
    });
    // 获取对资利率字典
    getDictByName("bankRate").then(res => {
      this.bankRate = res.content;
    });
    // 获取借款期限字典
    getDictByName("loanTerm").then(res => {
      this.loanTerm = res.content;
    });
    // 获取短信发送方字典
    getDictByName("SMSChannel").then(res => {
      this.SMSChannel = res.content;
    });
    // 获取风控模型渠道字典
    getDictByName("riskModelChannel").then(res => {
      this.riskModelChannel = res.content;
    });
    // 获取年龄范围字典
    getDictByName("ageRang").then(res => {
      this.ageRang = res.content;
    });
    // 获取征信上报方字典
    getDictByName("creditReport").then(res => {
      this.creditReport = res.content;
    });
    // 获取放款支付渠道字典
    getDictByName("loanPayChannel").then(res => {
      this.loanPayChannel = res.content;
    });
    // 获取扣款绑卡渠道字典
    getDictByName("deductChannel").then(res => {
      this.deductChannel = res.content;
    });
    // 获取逾期宽限期类型字典
    getDictByName("graceType").then(res => {
      this.graceType = res.content;
    });
    // 获取签署阶段字典
    getDictByName("LoanStage").then(res => {
      this.LoanStage = res.content;
    });
    this.queryAsset({pageNum: 1, pageSize: 99999,});
    this.queryGuarantee({pageNum: 1, pageSize: 99999,});
    this.queryFund({pageNum: 1, pageSize: 99999,});
    // 查询详情
    this.queryDetail({id: this.id});
    // 查询临时配置项列表
    this.queryConfigList(this.configQuery);
    this.queryAgreementList(this.agreementQuery)
  },

  methods: {

    // 查询项目详情
    queryDetail(params) {
      getDetail(params).then((res) => {
        if(res.code === '000000') {
          this.form = res.data || {};
          // 确保必要的属性存在
          if (!this.form.elements) {
            this.form.elements = {};
          }
          if (!this.form.elementsExt) {
            this.form.elementsExt = {};
          }
          if (!Array.isArray(this.form.loanTerms)) {
            this.form.loanTerms = [];
          }
          // 初始化新的时间字段（使用Vue.set确保响应性）
          if (this.form.elements.fundingCreditDarkHoursStart === undefined) {
            this.$set(this.form.elements, 'fundingCreditDarkHoursStart', null);
          }
          if (this.form.elements.fundingCreditDarkHoursEnd === undefined) {
            this.$set(this.form.elements, 'fundingCreditDarkHoursEnd', null);
          }
          if (this.form.elements.fundingLoanDarkHoursStart === undefined) {
            this.$set(this.form.elements, 'fundingLoanDarkHoursStart', null);
          }
          if (this.form.elements.fundingLoanDarkHoursEnd === undefined) {
            this.$set(this.form.elements, 'fundingLoanDarkHoursEnd', null);
          }
          if (this.form.elements.fundingRepayDarkHoursStart === undefined) {
            this.$set(this.form.elements, 'fundingRepayDarkHoursStart', null);
          }
          if (this.form.elements.fundingRepayDarkHoursEnd === undefined) {
            this.$set(this.form.elements, 'fundingRepayDarkHoursEnd', null);
          }
          if (this.form.elements.creditDarkHoursStart === undefined) {
            this.$set(this.form.elements, 'creditDarkHoursStart', null);
          }
          if (this.form.elements.creditDarkHoursEnd === undefined) {
            this.$set(this.form.elements, 'creditDarkHoursEnd', null);
          }
          if (this.form.elements.loanDarkHoursStart === undefined) {
            this.$set(this.form.elements, 'loanDarkHoursStart', null);
          }
          if (this.form.elements.loanDarkHoursEnd === undefined) {
            this.$set(this.form.elements, 'loanDarkHoursEnd', null);
          }
          if (this.form.elements.repayDarkHoursStart === undefined) {
            this.$set(this.form.elements, 'repayDarkHoursStart', null);
          }
          if (this.form.elements.repayDarkHoursEnd === undefined) {
            this.$set(this.form.elements, 'repayDarkHoursEnd', null);
          }
          // 将字符串格式的时间范围转换为时间选择器需要的数组格式（用于回显）
          this.parseFormTimeRangeForDisplay();
        };
      })
    },

    handleChange(val) {
      if(val === 'funds') {
        getFundOwer().then((res) => {
          if(res.code === '000000') {
            this.creditOwer = res.data
          };
        })
      } else {
        getGuaOwer().then((res) => {
          if(res.code === '000000') {
            this.creditOwer = res.data
          };
        })
      }
    },

    capitalLockChange(val) {
      if(val) {
        this.flowLock = true;
        this.agreementForm.flowLoanStage = null;
        this.agreementForm.flowContractName = null;
      } else {
        this.flowLock = false;
      }
    },

    flowLockChange(val) {
      if(val) {
        this.capitalLock = true;
        this.agreementForm.capitalLoanStage = null;
        this.agreementForm.capitalContractName = null;
      } else {
        this.capitalLock = false;
      }
    },

    // 点击页码及上一页下一页按钮操作
    currentConfigChange(val) {
      this.configQuery.pageNum = val;
      this.queryConfigList(this.configQuery); //调用接口方法
    },

    //每页展示几条按钮操作
    sizeConfigChange(val) {
      this.configQuery.pageSize = val;
      this.queryConfigList(this.configQuery); //调用接口方法
    },

    // 点击页码及上一页下一页按钮操作
    currentAgreementChange(val) {
      this.agreementQuery.pageNum = val;
      this.queryAgreementList(this.agreementQuery); //调用接口方法
    },

    //每页展示几条按钮操作
    sizeAgreementChange(val) {
      this.agreementQuery.pageSize = val;
      this.queryAgreementList(this.agreementQuery); //调用接口方法
    },

    // 查询临时配置项详情
    queryConfigDtl(params) {
      params.id = this.configSelect.temId;
      getConfigDtl(params).then((res) => {
        if(res.code === '000000') {
          this.configForm = res.data;
          // 将字符串格式的时间范围转换为时间选择器需要的数组格式
          this.parseTimeRangeForDisplay();
        };
      })
    },

    // 查询协议详情
    queryAgreementDtl(params) {
      params.id = this.agreementSelect.id;
      getAgreementDtl(params).then((res) => {
        if(res.code === '000000') {
          this.agreementForm = res.data;
        };
      })
    },

    // 查询对客协议列表
    queryAgreementList(params) {
      this.agreementLoading = true;
      params.projectCode = this.projectCode
      getAgreementList(params).then((res) => {
        if(res.code === '000000') {
          this.agreementData = res.data.content;
          this.agreementQuery.total = res.data.totalElements;
        };
        this.agreementLoading = false;
      })
    },

    // 将字符串格式的时间范围转换为时间选择器需要的格式（用于回显）
    parseTimeRangeForDisplay() {
      // 处理对客授信黑暗期
      if (this.configForm.creditDarkHours) {
        if (typeof this.configForm.creditDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.configForm.creditDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.configForm.creditDarkHoursStart = timeArray[0];
            this.configForm.creditDarkHoursEnd = timeArray[1];
          }
        } else if (Array.isArray(this.configForm.creditDarkHours) && this.configForm.creditDarkHours.length === 2) {
          this.configForm.creditDarkHoursStart = this.configForm.creditDarkHours[0];
          this.configForm.creditDarkHoursEnd = this.configForm.creditDarkHours[1];
        }
      }

      // 处理对客用信黑暗期
      if (this.configForm.loanDarkHours) {
        if (typeof this.configForm.loanDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.configForm.loanDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.configForm.loanDarkHoursStart = timeArray[0];
            this.configForm.loanDarkHoursEnd = timeArray[1];
          }
        } else if (Array.isArray(this.configForm.loanDarkHours) && this.configForm.loanDarkHours.length === 2) {
          this.configForm.loanDarkHoursStart = this.configForm.loanDarkHours[0];
          this.configForm.loanDarkHoursEnd = this.configForm.loanDarkHours[1];
        }
      }

      // 处理对客还款黑暗期
      if (this.configForm.repayDarkHours) {
        if (typeof this.configForm.repayDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.configForm.repayDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.configForm.repayDarkHoursStart = timeArray[0];
            this.configForm.repayDarkHoursEnd = timeArray[1];
          }
        } else if (Array.isArray(this.configForm.repayDarkHours) && this.configForm.repayDarkHours.length === 2) {
          this.configForm.repayDarkHoursStart = this.configForm.repayDarkHours[0];
          this.configForm.repayDarkHoursEnd = this.configForm.repayDarkHours[1];
        }
      }
    },

    // 解析单个时间字符串为时间数组
    parseSingleTimeString(timeStr) {
      if (typeof timeStr === 'string' && timeStr.includes('-')) {
        const times = timeStr.split('-');
        if (times.length === 2) {
          const today = new Date();
          const formatTime = (timeStr) => {
            const [hours, minutes] = timeStr.split(':');
            const time = new Date(today);
            time.setHours(parseInt(hours, 10));
            time.setMinutes(parseInt(minutes, 10));
            time.setSeconds(0);
            return time;
          };
          return [formatTime(times[0]), formatTime(times[1])];
        }
      }
      return [];
    },

    // 将基本要素的字符串格式时间范围转换为时间选择器需要的数组格式（用于回显）
    parseFormTimeRangeForDisplay() {
      // 处理对资授信黑暗期
      if (this.form.elements.fundingCreditDarkHours) {
        if (typeof this.form.elements.fundingCreditDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.form.elements.fundingCreditDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.$set(this.form.elements, 'fundingCreditDarkHoursStart', timeArray[0]);
            this.$set(this.form.elements, 'fundingCreditDarkHoursEnd', timeArray[1]);
          }
        } else if (Array.isArray(this.form.elements.fundingCreditDarkHours) && this.form.elements.fundingCreditDarkHours.length === 2) {
          // 处理数组格式的时间数据
          this.$set(this.form.elements, 'fundingCreditDarkHoursStart', this.form.elements.fundingCreditDarkHours[0]);
          this.$set(this.form.elements, 'fundingCreditDarkHoursEnd', this.form.elements.fundingCreditDarkHours[1]);
        }
      }

      // 处理对资用信黑暗期
      if (this.form.elements.fundingLoanDarkHours) {
        if (typeof this.form.elements.fundingLoanDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.form.elements.fundingLoanDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.$set(this.form.elements, 'fundingLoanDarkHoursStart', timeArray[0]);
            this.$set(this.form.elements, 'fundingLoanDarkHoursEnd', timeArray[1]);
          }
        } else if (Array.isArray(this.form.elements.fundingLoanDarkHours) && this.form.elements.fundingLoanDarkHours.length === 2) {
          this.$set(this.form.elements, 'fundingLoanDarkHoursStart', this.form.elements.fundingLoanDarkHours[0]);
          this.$set(this.form.elements, 'fundingLoanDarkHoursEnd', this.form.elements.fundingLoanDarkHours[1]);
        }
      }

      // 处理对资还款黑暗期
      if (this.form.elements.fundingRepayDarkHours) {
        if (typeof this.form.elements.fundingRepayDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.form.elements.fundingRepayDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.$set(this.form.elements, 'fundingRepayDarkHoursStart', timeArray[0]);
            this.$set(this.form.elements, 'fundingRepayDarkHoursEnd', timeArray[1]);
          }
        } else if (Array.isArray(this.form.elements.fundingRepayDarkHours) && this.form.elements.fundingRepayDarkHours.length === 2) {
          this.$set(this.form.elements, 'fundingRepayDarkHoursStart', this.form.elements.fundingRepayDarkHours[0]);
          this.$set(this.form.elements, 'fundingRepayDarkHoursEnd', this.form.elements.fundingRepayDarkHours[1]);
        }
      }

      // 处理对客授信黑暗期
      if (this.form.elements.creditDarkHours) {
        if (typeof this.form.elements.creditDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.form.elements.creditDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.$set(this.form.elements, 'creditDarkHoursStart', timeArray[0]);
            this.$set(this.form.elements, 'creditDarkHoursEnd', timeArray[1]);
          }
        } else if (Array.isArray(this.form.elements.creditDarkHours) && this.form.elements.creditDarkHours.length === 2) {
          this.$set(this.form.elements, 'creditDarkHoursStart', this.form.elements.creditDarkHours[0]);
          this.$set(this.form.elements, 'creditDarkHoursEnd', this.form.elements.creditDarkHours[1]);
        }
      }

      // 处理对客用信黑暗期
      if (this.form.elements.loanDarkHours) {
        if (typeof this.form.elements.loanDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.form.elements.loanDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.$set(this.form.elements, 'loanDarkHoursStart', timeArray[0]);
            this.$set(this.form.elements, 'loanDarkHoursEnd', timeArray[1]);
          }
        } else if (Array.isArray(this.form.elements.loanDarkHours) && this.form.elements.loanDarkHours.length === 2) {
          this.$set(this.form.elements, 'loanDarkHoursStart', this.form.elements.loanDarkHours[0]);
          this.$set(this.form.elements, 'loanDarkHoursEnd', this.form.elements.loanDarkHours[1]);
        }
      }

      // 处理对客还款黑暗期
      if (this.form.elements.repayDarkHours) {
        if (typeof this.form.elements.repayDarkHours === 'string') {
          const timeArray = this.parseSingleTimeString(this.form.elements.repayDarkHours);
          if (timeArray && timeArray.length === 2) {
            this.$set(this.form.elements, 'repayDarkHoursStart', timeArray[0]);
            this.$set(this.form.elements, 'repayDarkHoursEnd', timeArray[1]);
          }
        } else if (Array.isArray(this.form.elements.repayDarkHours) && this.form.elements.repayDarkHours.length === 2) {
          this.$set(this.form.elements, 'repayDarkHoursStart', this.form.elements.repayDarkHours[0]);
          this.$set(this.form.elements, 'repayDarkHoursEnd', this.form.elements.repayDarkHours[1]);
        }
      }
    },

    // 将时间选择器的数组格式转换为中间变量的字符串格式（用于提交）
    formatTimeRangeForSubmit() {
      // 处理对客授信黑暗期
      if (this.configForm.creditDarkHoursStart && this.configForm.creditDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.configForm.creditDarkHoursStart);
        const endTime = this.formatSingleTime(this.configForm.creditDarkHoursEnd);
        this.submitData.creditDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.configForm.creditDarkHours && this.configForm.creditDarkHours.length === 2) {
        this.submitData.creditDarkHoursStr = formatTimeRange(this.configForm.creditDarkHours);
      }

      // 处理对客用信黑暗期
      if (this.configForm.loanDarkHoursStart && this.configForm.loanDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.configForm.loanDarkHoursStart);
        const endTime = this.formatSingleTime(this.configForm.loanDarkHoursEnd);
        this.submitData.loanDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.configForm.loanDarkHours && this.configForm.loanDarkHours.length === 2) {
        this.submitData.loanDarkHoursStr = formatTimeRange(this.configForm.loanDarkHours);
      }

      // 处理对客还款黑暗期
      if (this.configForm.repayDarkHoursStart && this.configForm.repayDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.configForm.repayDarkHoursStart);
        const endTime = this.formatSingleTime(this.configForm.repayDarkHoursEnd);
        this.submitData.repayDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.configForm.repayDarkHours && this.configForm.repayDarkHours.length === 2) {
        this.submitData.repayDarkHoursStr = formatTimeRange(this.configForm.repayDarkHours);
      }
    },

    // 验证对客授信黑暗期
    validateCreditDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.validateField('creditDarkHours');
        }
      });
    },

    // 验证对客用信黑暗期
    validateLoanDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.validateField('loanDarkHours');
        }
      });
    },

    // 验证对客还款黑暗期
    validateRepayDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.validateField('repayDarkHours');
        }
      });
    },

    // 验证对资授信黑暗期
    validateFundingCreditDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.baseForm) {
          this.$refs.baseForm.validateField('elements.fundingCreditDarkHours');
        }
      });
    },

    // 验证对资用信黑暗期
    validateFundingLoanDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.baseForm) {
          this.$refs.baseForm.validateField('elements.fundingLoanDarkHours');
        }
      });
    },

    // 验证对资还款黑暗期
    validateFundingRepayDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.baseForm) {
          this.$refs.baseForm.validateField('elements.fundingRepayDarkHours');
        }
      });
    },

    // 验证对客授信黑暗期（基本要素）
    validateElementsCreditDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.baseForm) {
          this.$refs.baseForm.validateField('elements.creditDarkHours');
        }
      });
    },

    // 验证对客用信黑暗期（基本要素）
    validateElementsLoanDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.baseForm) {
          this.$refs.baseForm.validateField('elements.loanDarkHours');
        }
      });
    },

    // 验证对客还款黑暗期（基本要素）
    validateElementsRepayDarkHours() {
      // 手动触发表单验证
      this.$nextTick(() => {
        if (this.$refs.baseForm) {
          this.$refs.baseForm.validateField('elements.repayDarkHours');
        }
      });
    },

    // 格式化单个时间为HH:mm格式
    formatSingleTime(timeDate) {
      if (!timeDate) return '';
      const hours = timeDate.getHours();
      const minutes = timeDate.getMinutes();
      const hourStr = hours < 10 ? '0' + hours : hours.toString();
      const minuteStr = minutes < 10 ? '0' + minutes : minutes.toString();
      return `${hourStr}:${minuteStr}`;
    },

    // 将基本要素时间选择器的数组格式转换为中间变量的字符串格式（用于提交）
    formatFormTimeRangeForSubmit() {
      // 处理对资授信黑暗期
      if (this.form.elements.fundingCreditDarkHoursStart && this.form.elements.fundingCreditDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.form.elements.fundingCreditDarkHoursStart);
        const endTime = this.formatSingleTime(this.form.elements.fundingCreditDarkHoursEnd);
        this.formSubmitData.fundingCreditDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.form.elements.fundingCreditDarkHours && this.form.elements.fundingCreditDarkHours.length === 2) {
        this.formSubmitData.fundingCreditDarkHoursStr = formatTimeRange(this.form.elements.fundingCreditDarkHours);
      }

      // 处理对资用信黑暗期
      if (this.form.elements.fundingLoanDarkHoursStart && this.form.elements.fundingLoanDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.form.elements.fundingLoanDarkHoursStart);
        const endTime = this.formatSingleTime(this.form.elements.fundingLoanDarkHoursEnd);
        this.formSubmitData.fundingLoanDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.form.elements.fundingLoanDarkHours && this.form.elements.fundingLoanDarkHours.length === 2) {
        this.formSubmitData.fundingLoanDarkHoursStr = formatTimeRange(this.form.elements.fundingLoanDarkHours);
      }

      // 处理对资还款黑暗期
      if (this.form.elements.fundingRepayDarkHoursStart && this.form.elements.fundingRepayDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.form.elements.fundingRepayDarkHoursStart);
        const endTime = this.formatSingleTime(this.form.elements.fundingRepayDarkHoursEnd);
        this.formSubmitData.fundingRepayDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.form.elements.fundingRepayDarkHours && this.form.elements.fundingRepayDarkHours.length === 2) {
        this.formSubmitData.fundingRepayDarkHoursStr = formatTimeRange(this.form.elements.fundingRepayDarkHours);
      }

      // 处理对客授信黑暗期
      if (this.form.elements.creditDarkHoursStart && this.form.elements.creditDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.form.elements.creditDarkHoursStart);
        const endTime = this.formatSingleTime(this.form.elements.creditDarkHoursEnd);
        this.formSubmitData.creditDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.form.elements.creditDarkHours && this.form.elements.creditDarkHours.length === 2) {
        this.formSubmitData.creditDarkHoursStr = formatTimeRange(this.form.elements.creditDarkHours);
      }

      // 处理对客用信黑暗期
      if (this.form.elements.loanDarkHoursStart && this.form.elements.loanDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.form.elements.loanDarkHoursStart);
        const endTime = this.formatSingleTime(this.form.elements.loanDarkHoursEnd);
        this.formSubmitData.loanDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.form.elements.loanDarkHours && this.form.elements.loanDarkHours.length === 2) {
        this.formSubmitData.loanDarkHoursStr = formatTimeRange(this.form.elements.loanDarkHours);
      }

      // 处理对客还款黑暗期
      if (this.form.elements.repayDarkHoursStart && this.form.elements.repayDarkHoursEnd) {
        const startTime = this.formatSingleTime(this.form.elements.repayDarkHoursStart);
        const endTime = this.formatSingleTime(this.form.elements.repayDarkHoursEnd);
        this.formSubmitData.repayDarkHoursStr = `${startTime}-${endTime}`;
      } else if (this.form.elements.repayDarkHours && this.form.elements.repayDarkHours.length === 2) {
        this.formSubmitData.repayDarkHoursStr = formatTimeRange(this.form.elements.repayDarkHours);
      }
    },



    // 查询临时配置项列表
    queryConfigList(params) {
      this.configLoading = true;
      params.projectCode = this.projectCode
      getConfigList(params).then((res) => {
        if(res.code === '000000') {
          this.configData = res.data.content;
          this.configQuery.total = res.data.totalElements;
        };
        this.configLoading = false;
      })
    },

    // 查询资产方主列表
    queryAsset(params) {
      getAssetList(params).then((res) => {
        if(res.code === '000000') {
          this.assetList = res.data.content;
        };
      })
    },

    // 查询融担方主列表
    queryGuarantee(params) {
      getGuaranteeList(params).then((res) => {
        if(res.code === '000000') {
          this.guaranteeList = res.data.content;
        };
      })
    },

    // 查询资金方主列表
    queryFund(params) {
      getFundList(params).then((res) => {
        if(res.code === '000000') {
          this.fundList = res.data.content;
        };
      })
    },

    // 修改项目
    editProject() {
      let resule = true;
      const refs = ['proForm','baseForm','creForm','loanForm','repayForm'];
      for(const ref of refs) {
        this.$refs[ref].validate(valid => {
          resule = resule && valid;
        });
        if(!resule) break;
      }
      if(resule) {
        // 在提交前将基本要素时间选择器的数组格式转换为字符串格式
        this.formatFormTimeRangeForSubmit();

        // 创建提交数据，使用中间变量的字符串格式
        const submitForm = {
          ...this.form,
          elements: {
            ...this.form.elements,
            fundingCreditDarkHours: this.formSubmitData.fundingCreditDarkHoursStr,
            fundingLoanDarkHours: this.formSubmitData.fundingLoanDarkHoursStr,
            fundingRepayDarkHours: this.formSubmitData.fundingRepayDarkHoursStr,
            creditDarkHours: this.formSubmitData.creditDarkHoursStr,
            loanDarkHours: this.formSubmitData.loanDarkHoursStr,
            repayDarkHours: this.formSubmitData.repayDarkHoursStr
          }
        };
        submitForm.projectCode = this.projectCode;
        submitForm.elementsExt.projectCode = this.projectCode;
        submitForm.elementsExt.projectName = this.form.elements.projectName;
        updateProject(submitForm).then((res) => {
          if(res.code === '000000') {
            this.goBack();
            this.$message({
              message: '项目修改成功!',
              type: 'success'
            });
          } else {
            this.$message({
              message: '项目修改失败!',
              type: 'error'
            });
          }
        })
      }
    },

    goBack() {
      this.$router.back();
    },

    configCancel() {
      this.configDialog = false;
      this.configForm = {
        creditDarkHoursStart: null,
        creditDarkHoursEnd: null,
        loanDarkHoursStart: null,
        loanDarkHoursEnd: null,
        repayDarkHoursStart: null,
        repayDarkHoursEnd: null
      };
      // 重置临时配置项中间变量
      this.submitData = {
        creditDarkHoursStr: '',
        loanDarkHoursStr: '',
        repayDarkHoursStr: ''
      };
      // 重置基本要素中间变量
      this.formSubmitData = {
        fundingCreditDarkHoursStr: '',
        fundingLoanDarkHoursStr: '',
        fundingRepayDarkHoursStr: '',
        creditDarkHoursStr: '',
        loanDarkHoursStr: '',
        repayDarkHoursStr: ''
      };
    },

    agreementCancel() {
      this.agreementDialog = false;
      this.agreementForm = {};
      this.flowLock = false;
      this.capitalLock = false;
      this.$refs.file.value = null;
    },

    uploadFile(e) {
      let file = e.target.files[0];
      let fordata = new FormData();
      fordata.append('file', file);
      upload(fordata).then((res) => {
        if(res.code === '000000') {
          this.adder = res.data;
          this.$message({
            message: '合同上传成功!',
            type: 'success'
          });
        } else {
          this.$message({
            message: '合同上传失败!',
            type: 'error'
          });
        }
      })
    },

    // 新增临时配置项
    configSubmit() {
      // 在提交前将时间选择器的数组格式转换为中间变量的字符串格式
      this.formatTimeRangeForSubmit();

      if(this.configType === 'add') {
        this.$refs['configForm'].validate(valid => {
          if (valid) {
            this.configForm.projectCode = this.projectCode;
            this.configForm.creditDarkHours =  this.submitData.creditDarkHoursStr,
            this.configForm.loanDarkHours = this.submitData.loanDarkHoursStr,
            this.configForm.repayDarkHours = this.submitData.repayDarkHoursStr,
            addConfig(this.configForm).then((res) => {
              if(res.code === '000000') {
                this.configDialog = false;
                this.configForm = {
                  creditDarkHoursStart: null,
                  creditDarkHoursEnd: null,
                  loanDarkHoursStart: null,
                  loanDarkHoursEnd: null,
                  repayDarkHoursStart: null,
                  repayDarkHoursEnd: null
                };
                // 重置临时配置项中间变量
                this.submitData = {
                  creditDarkHoursStr: '',
                  loanDarkHoursStr: '',
                  repayDarkHoursStr: ''
                };
                // 重置基本要素中间变量
                this.formSubmitData = {
                  fundingCreditDarkHoursStr: '',
                  fundingLoanDarkHoursStr: '',
                  fundingRepayDarkHoursStr: '',
                  creditDarkHoursStr: '',
                  loanDarkHoursStr: '',
                  repayDarkHoursStr: ''
                };
                this.queryConfigList(this.configQuery);
                this.$message({
                  message: '临时配置项新增成功!',
                  type: 'success'
                });
              } else {
                this.$message({
                  message: '临时配置项新增失败!',
                  type: 'error'
                });
              }
            })
          }
        })
      } else {
        this.$refs['configForm'].validate(valid => {
          if (valid) {
            this.configForm.id = this.configSelect.temId;
            // 创建提交数据，使用中间变量的字符串格式
            const submitForm = {
              ...this.configForm,
              creditDarkHours: this.submitData.creditDarkHoursStr,
              loanDarkHours: this.submitData.loanDarkHoursStr,
              repayDarkHours: this.submitData.repayDarkHoursStr
            };

            updateTemProject(submitForm).then((res) => {
              if(res.code === '000000') {
                this.configDialog = false;
                this.configForm = {
                  creditDarkHoursStart: null,
                  creditDarkHoursEnd: null,
                  loanDarkHoursStart: null,
                  loanDarkHoursEnd: null,
                  repayDarkHoursStart: null,
                  repayDarkHoursEnd: null
                };
                // 重置临时配置项中间变量
                this.submitData = {
                  creditDarkHoursStr: '',
                  loanDarkHoursStr: '',
                  repayDarkHoursStr: ''
                };
                // 重置基本要素中间变量
                this.formSubmitData = {
                  fundingCreditDarkHoursStr: '',
                  fundingLoanDarkHoursStr: '',
                  fundingRepayDarkHoursStr: '',
                  creditDarkHoursStr: '',
                  loanDarkHoursStr: '',
                  repayDarkHoursStr: ''
                };
                this.queryConfigList(this.configQuery);
                this.$message({
                  message: '临时项目修改成功!',
                  type: 'success'
                });
              } else {
                this.$message({
                  message: '临时项目修改失败!',
                  type: 'error'
                });
              }
            })
          }
        })
      }
    },

    // 新增协议
    agreementSubmit() {
      if(this.agreementType === 'add') {
        this.agreementForm.projectCode = this.projectCode;
        this.agreementForm.filePath = this.adder;
        this.$refs['agreementForm'].validate(valid => {
          if(valid) {
            addAgreement(this.agreementForm).then((res) => {
              if(res.code === '000000') {
                this.agreementDialog = false;
                this.agreementForm = {};
                this.queryAgreementList(this.agreementQuery);
                this.$message({
                  message: '对客协议新增成功!',
                  type: 'success'
                });
              } else {
                this.$message({
                  message: '对客协议新增失败!',
                  type: 'error'
                });
              }
            })
            this.$refs.file.value = null;
          }
        })
      } else {
        this.$refs['agreementForm'].validate(valid => {
          if(valid) {
            this.agreementForm.id = this.agreementSelect.id;
            updateAgreement(this.agreementForm).then((res) => {
              if(res.code === '000000') {
                this.agreementDialog = false;
                this.agreementForm = {};
                this.queryAgreementList(this.configQuery);
                this.$message({
                  message: '对客协议修改成功!',
                  type: 'success'
                });
              } else {
                this.$message({
                  message: '对客协议修改失败!',
                  type: 'error'
                });
              }
            })
            this.$refs.file.value = null;
          }
        })
      }
      this.flowLock = false;
      this.capitalLock = false;
    },

    // 启用/停用临时配置
    stopUseConfig() {
      if (Object.keys(this.configSelect).length !== 0) {
        this.$confirm("确定启用/停用当前项目？","警告",{type:"warning"}).then(()=>{
          const params = {
            id: this.configSelect.temId,
            enabled: this.configSelect.temEnabled === 'INIT' ? 'ENABLE' : this.configSelect.temEnabled === 'ENABLE' ? 'DISABLE' : 'ENABLE'
          }
          updateTemProject(params).then((res) => {
            if(res.code === '000000') {
              this.queryConfigList(this.configQuery);
              this.$message({
                message: '临时项目启用/停用成功!',
                type: 'success'
              });
            } else {
              this.$message({
                message: '临时项目启用/停用失败!',
                type: 'error'
              });
            }
          })
        }).catch(()=>{

        });
      } else {
        this.$message({
          message: '请选择一个项目!',
          type: 'warning'
        });
      }
    },

    // 启用/停用协议
    stopUseAgreement() {
      if (Object.keys(this.agreementSelect).length !== 0) {
        this.$confirm("确定启用/停用当前协议？","警告",{type:"warning"}).then(()=>{
          const params = {
            id: this.agreementSelect.id,
            enabled: this.agreementSelect.enabled === 'INIT' ? 'ENABLE' : this.agreementSelect.enabled === 'ENABLE' ? 'DISABLE' : 'ENABLE'
          }
          updateAgreement(params).then((res) => {
            if(res.code === '000000') {
              this.queryAgreementList(this.agreementQuery);
              this.$message({
                message: '协议启用/停用成功!',
                type: 'success'
              });
            } else {
              this.$message({
                message: '协议启用/停用失败!',
                type: 'error'
              });
            }
          })
        }).catch(()=>{

        });
      } else {
        this.$message({
          message: '请选择一个项目!',
          type: 'warning'
        });
      }
    },

    // 打开临时配置项弹窗
    openConfigDialog(type) {
      if(type !== 'add') {
        if (Object.keys(this.configSelect).length !== 0) {
          this.queryConfigDtl(this.configQuery)
          this.configType = type;
          this.configDialog = true;
        } else {
          this.$message({
            message: '请选择一个项目!',
            type: 'warning'
          });
        }
      } else {
          this.configType = type;
          this.configDialog = true;
      }
    },

    openAgreementDialog(type) {
      if(type !== 'add') {
        if (Object.keys(this.agreementSelect).length !== 0) {
          this.queryAgreementDtl(this.agreementQuery)
          this.agreementType = type;
          this.agreementDialog = true;
        } else {
          this.$message({
            message: '请选择一个项目!',
            type: 'warning'
          });
        }
      } else {
          this.agreementType = type;
          this.agreementDialog = true;
      }
    },

    // 搜索对客协议
    search() {
      this.queryAgreementList(this.agreementQuery);
    },

    // 重置搜索条件
    reset() {
      this.agreementQuery = {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      this.queryAgreementList(this.agreementQuery);
    },

    // 控制单选
    handleConfigChange(val) {
      if (val.length > 1) {
        this.$refs.configTable.clearSelection();
        this.$refs.configTable.toggleRowSelection(val[val.length - 1]);
      }
      this.configSelect = val[val.length - 1];
      if(!this.configSelect) {
        this.configSelect = {}
      }
    },
    // 控制单选
    handleAgreementChange(val) {
      if (val.length > 1) {
        this.$refs.AgreementTable.clearSelection();
        this.$refs.AgreementTable.toggleRowSelection(val[val.length - 1]);
      }
      this.agreementSelect = val[val.length - 1];
      if(!this.agreementSelect) {
        this.agreementSelect = {}
      }
    },

    // 删除协议
    deleteAgreement() {
      if (Object.keys(this.agreementSelect).length !== 0) {
        delAgreement([this.agreementSelect.id]).then((res) => {
          if(res.code === '000000') {
            this.queryAgreementList(this.agreementQuery);
            this.$message({
              message: '协议删除成功!',
              type: 'success'
            });
          } else {
            this.$message({
              message: '协议删除失败!',
              type: 'error'
            });
          }
        })
      } else {
        this.$message({
          message: '请选择一个协议!',
          type: 'warning'
        });
      }
    },

  }
}
</script>

<style scoped>
::v-deep .el-collapse-item__header{
  font-size: 18px;
  font-weight: 600;
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
</style>
